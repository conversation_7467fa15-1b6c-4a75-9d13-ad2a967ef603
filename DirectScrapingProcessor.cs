using System.Text.Json;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Processeur pour le scraping direct avec génération de posts Facebook
    /// </summary>
    public class DirectScrapingProcessor
    {
        private readonly DirectScrapingExtractor _directExtractor;
        private readonly FacebookPostGenerator _postGenerator;
        private readonly ProcessingOptions _options;

        public DirectScrapingProcessor(ProcessingOptions options, string geminiApiKey)
        {
            _options = options;
            
            // Créer l'extracteur direct
            var amazonLoader = new AmazonLoader<string>(_options.HeadlessMode);
            
            // Créer l'extracteur IA pour le fallback si nécessaire
            AiBasedExtractor? aiExtractor = null;
            if (!string.IsNullOrEmpty(geminiApiKey))
            {
                aiExtractor = new AiBasedExtractor(amazonLoader);
                // Note: Il faudrait configurer la clé API Gemini pour l'extracteur IA
            }

            _directExtractor = new DirectScrapingExtractor(amazonLoader, aiExtractor);
            _postGenerator = new FacebookPostGenerator(geminiApiKey);
        }

        /// <summary>
        /// Processus complet : scraping direct + génération de posts
        /// </summary>
        public async Task ProcessCompleteWorkflow(bool useAiFallback = true)
        {
            try
            {
                Console.WriteLine("🚀 DÉMARRAGE DU PROCESSUS COMPLET SCRAPING DIRECT");
                Console.WriteLine();

                // 1. Extraction des produits avec scraping direct
                Console.WriteLine("⚡ Phase 1: Extraction des produits avec scraping direct...");
                var products = await _directExtractor.ExtractAllDealsProducts(_options.MaxPages, useAiFallback);

                if (products.Count == 0)
                {
                    Console.WriteLine("❌ Aucun produit trouvé. Arrêt du processus.");
                    return;
                }

                Console.WriteLine($"✅ {products.Count} produits extraits avec succès !");
                Console.WriteLine();

                // 2. Génération des liens d'affiliation
                Console.WriteLine("🔗 Phase 2: Génération des liens d'affiliation...");
                _directExtractor.GenerateAffiliateLinks(_options.AmazonAssociateTag);
                Console.WriteLine("✅ Liens d'affiliation générés !");
                Console.WriteLine();

                // 3. Sauvegarde des produits en JSON si demandé
                if (_options.SaveProductsJson)
                {
                    Console.WriteLine("💾 Phase 3: Sauvegarde des produits en JSON...");
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var jsonPath = Path.Combine(_options.OutputDirectory, $"direct_scraping_products_{timestamp}.json");
                    
                    if (!Directory.Exists(_options.OutputDirectory))
                    {
                        Directory.CreateDirectory(_options.OutputDirectory);
                    }
                    
                    await _directExtractor.SaveProductsToJson(jsonPath);
                    Console.WriteLine($"✅ Produits sauvegardés: {jsonPath}");
                    Console.WriteLine();
                }

                // 4. Génération des posts Facebook si demandé
                if (_options.GeneratePosts)
                {
                    Console.WriteLine("📝 Phase 4: Génération des posts Facebook...");
                    await GenerateFacebookPostsForAllProducts(products);
                    Console.WriteLine("✅ Posts Facebook générés !");
                }

                Console.WriteLine();
                Console.WriteLine("🎉 PROCESSUS COMPLET TERMINÉ AVEC SUCCÈS !");
                Console.WriteLine($"   📊 Produits traités: {products.Count}");
                Console.WriteLine($"   📁 Dossier de sortie: {_options.OutputDirectory}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans le processus complet: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Génère des posts Facebook pour tous les produits extraits
        /// </summary>
        private async Task GenerateFacebookPostsForAllProducts(List<ProductInfo> products)
        {
            if (!Directory.Exists(_options.OutputDirectory))
            {
                Directory.CreateDirectory(_options.OutputDirectory);
            }

            Console.WriteLine($"📝 Génération de {products.Count} posts Facebook...");

            for (int i = 0; i < products.Count; i++)
            {
                var product = products[i];
                
                try
                {
                    Console.WriteLine($"📝 Génération du post {i + 1}/{products.Count} : {product.Title[..Math.Min(50, product.Title.Length)]}...");
                    
                    // Générer le post Facebook
                    var post = await _postGenerator.GeneratePostFromProductInfo(product);
                    
                    // Sauvegarder le post
                    var fileName = $"post_{i + 1:D3}_{SanitizeFileName(product.Title[..Math.Min(30, product.Title.Length)])}.txt";
                    var filePath = Path.Combine(_options.OutputDirectory, fileName);
                    
                    var postContent = CreatePostFileContent(product, post);
                    await File.WriteAllTextAsync(filePath, postContent);
                    
                    Console.WriteLine($"✅ Post sauvegardé : {fileName}");
                    
                    // Délai entre les générations pour éviter les limites de taux
                    if (i < products.Count - 1) // Pas de délai après le dernier
                    {
                        await Task.Delay(_options.DelayBetweenPosts);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur pour le produit {i + 1} : {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Crée le contenu complet du fichier de post
        /// </summary>
        private string CreatePostFileContent(ProductInfo product, string facebookPost)
        {
            var content = $"=== INFORMATIONS PRODUIT ===\n";
            content += $"Titre: {product.Title}\n";
            content += $"URL: {product.ProductUrl}\n";
            content += $"Lien d'affiliation: {product.AffiliateLink}\n";
            content += $"Prix: {product.Price}\n";
            content += $"Prix original: {product.OriginalPrice}\n";
            content += $"Réduction: {product.Discount}\n";
            content += $"Note: {product.Rating}\n";
            content += $"Avis: {product.ReviewCount}\n";
            content += $"Image: {product.ImageUrl}\n";
            content += $"\n=== POST FACEBOOK ===\n\n{facebookPost}";
            
            return content;
        }

        /// <summary>
        /// Nettoie un nom de fichier en supprimant les caractères invalides
        /// </summary>
        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
            return sanitized.Length > 50 ? sanitized[..50] : sanitized;
        }

        /// <summary>
        /// Traite les produits depuis un fichier JSON existant
        /// </summary>
        public async Task ProcessFromJson(string jsonPath)
        {
            try
            {
                Console.WriteLine($"📂 Chargement des produits depuis: {jsonPath}");
                
                var jsonContent = await File.ReadAllTextAsync(jsonPath);
                var products = JsonSerializer.Deserialize<List<ProductInfo>>(jsonContent);
                
                if (products == null || products.Count == 0)
                {
                    Console.WriteLine("❌ Aucun produit trouvé dans le fichier JSON");
                    return;
                }

                Console.WriteLine($"✅ {products.Count} produits chargés depuis le JSON");

                // Vérifier/générer les liens d'affiliation si nécessaire
                bool needsAffiliateLinks = products.Any(p => string.IsNullOrEmpty(p.AffiliateLink));
                if (needsAffiliateLinks)
                {
                    Console.WriteLine("🔗 Génération des liens d'affiliation manquants...");
                    foreach (var product in products.Where(p => string.IsNullOrEmpty(p.AffiliateLink)))
                    {
                        if (!string.IsNullOrEmpty(product.ProductUrl))
                        {
                            var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                            if (asinMatch.Success)
                            {
                                var asin = asinMatch.Groups[1].Value;
                                product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={_options.AmazonAssociateTag}";
                            }
                        }
                    }
                }

                // Générer les posts Facebook
                if (_options.GeneratePosts)
                {
                    await GenerateFacebookPostsForAllProducts(products);
                }

                Console.WriteLine("✅ Traitement depuis JSON terminé !");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du traitement JSON: {ex.Message}");
                throw;
            }
        }
    }
}
