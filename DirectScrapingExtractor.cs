using PuppeteerSharp;
using System.Text.Json;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Extracteur avec scraping direct optimisé et option IA en fallback
    /// Utilise les sélecteurs CSS spécifiques identifiés pour Amazon
    /// </summary>
    public class DirectScrapingExtractor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private readonly AiBasedExtractor? _aiExtractor;
        private readonly List<ProductInfo> _extractedProducts = new();
        private readonly Random _random = new();

        // Configuration
        private const string DEALS_URL = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
        private const int MAX_SCROLL_ATTEMPTS = 20;
        private const int SCROLL_DELAY_MIN = 1000;
        private const int SCROLL_DELAY_MAX = 3000;

        public DirectScrapingExtractor(AmazonLoader<string> amazonLoader, AiBasedExtractor? aiExtractor = null)
        {
            _amazonLoader = amazonLoader;
            _aiExtractor = aiExtractor;
        }

        /// <summary>
        /// Extrait tous les produits en utilisant le scraping direct avec fallback IA
        /// </summary>
        public async Task<List<ProductInfo>> ExtractAllDealsProducts(int maxPages = 5, bool useAiFallback = false)
        {
            _extractedProducts.Clear();
            
            Console.WriteLine("🚀 DÉMARRAGE DU SCRAPING DIRECT OPTIMISÉ");
            Console.WriteLine($"   📄 Pages maximum: {maxPages}");
            Console.WriteLine($"   🤖 Fallback IA: {(useAiFallback && _aiExtractor != null ? "Activé" : "Désactivé")}");
            Console.WriteLine();

            try
            {
                await _amazonLoader.TryAndExecute(DEALS_URL);
                var page = _amazonLoader.GetCurrentPage();
                
                if (page == null)
                {
                    throw new InvalidOperationException("Impossible d'accéder à la page Amazon Deals");
                }

                int currentPage = 1;
                int totalProductsFound = 0;

                while (currentPage <= maxPages)
                {
                    Console.WriteLine($"📄 Traitement de la page {currentPage}/{maxPages}...");
                    
                    // Attendre que la page soit chargée
                    await Task.Delay(2000);
                    
                    // Extraire les produits avec scraping direct
                    var pageProducts = await ExtractProductsFromCurrentPageDirect(page);
                    
                    if (pageProducts.Count == 0 && useAiFallback && _aiExtractor != null)
                    {
                        Console.WriteLine("   ⚠️ Scraping direct n'a trouvé aucun produit, tentative avec IA...");
                        // Fallback vers l'extracteur IA
                        pageProducts = await ExtractWithAiFallback(page);
                    }
                    
                    if (pageProducts.Count > 0)
                    {
                        _extractedProducts.AddRange(pageProducts);
                        totalProductsFound += pageProducts.Count;
                        Console.WriteLine($"   ✅ {pageProducts.Count} produits extraits (Total: {totalProductsFound})");
                    }
                    else
                    {
                        Console.WriteLine("   ⚠️ Aucun produit trouvé sur cette page");
                    }

                    // Essayer de charger plus de contenu avec le bouton "Afficher plus d'offres"
                    if (currentPage == 1)
                    {
                        await LoadMoreDeals(page);
                    }

                    // Essayer de naviguer vers la page suivante
                    if (currentPage < maxPages)
                    {
                        bool hasNextPage = await NavigateToNextPage(page);
                        if (!hasNextPage)
                        {
                            Console.WriteLine("   ℹ️ Pas de page suivante disponible");
                            break;
                        }
                    }

                    currentPage++;
                    
                    // Délai entre les pages
                    await Task.Delay(_random.Next(2000, 5000));
                }

                Console.WriteLine();
                Console.WriteLine($"🎯 EXTRACTION TERMINÉE: {_extractedProducts.Count} produits trouvés au total");
                
                return _extractedProducts;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de l'extraction: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Extrait les produits de la page actuelle avec scraping direct optimisé
        /// </summary>
        private async Task<List<ProductInfo>> ExtractProductsFromCurrentPageDirect(IPage page)
        {
            var products = await page.EvaluateFunctionAsync<ProductInfo[]>(@"() => {
                const products = [];
                
                console.log('🔍 SCRAPING DIRECT - Recherche des produits...');

                // Sélecteurs prioritaires basés sur la structure Amazon identifiée
                const productSelectors = [
                    // PRIORITÉ 1: Cartes de produits avec data-asin
                    'div[class*=""ProductCard-module__card""][data-asin]',
                    'div[class^=""ProductCard-module__card""][data-asin]',
                    // PRIORITÉ 2: Conteneurs avec data-asin
                    '[data-asin]:not([data-asin=""""])',
                    // PRIORITÉ 3: Fallback vers les anciens sélecteurs
                    '[data-csa-c-type=""widget""] [data-asin]',
                    '[data-csa-c-slot-id] [data-asin]',
                    '.a-carousel-card',
                    '.p13n-desktop-carousel-cards [data-asin]'
                ];

                let productElements = [];
                let usedSelector = '';

                // Essayer les sélecteurs par ordre de priorité
                for (const selector of productSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        productElements = Array.from(elements);
                        usedSelector = selector;
                        console.log(`✅ Sélecteur utilisé: ${selector} (${elements.length} éléments)`);
                        break;
                    }
                }

                if (productElements.length === 0) {
                    console.log('❌ Aucun produit trouvé avec les sélecteurs directs');
                    return [];
                }

                console.log(`🎯 ${productElements.length} éléments produits détectés`);

                productElements.forEach((element, index) => {
                    try {
                        const product = {
                            Title: '',
                            ProductUrl: '',
                            ImageUrl: '',
                            Price: '',
                            OriginalPrice: '',
                            Discount: '',
                            Rating: '',
                            ReviewCount: '',
                            AffiliateLink: '',
                            IsDeal: true
                        };

                        // 1. ASIN et URL du produit
                        const asin = element.getAttribute('data-asin');
                        if (asin && asin.length === 10) {
                            product.ProductUrl = `https://www.amazon.fr/dp/${asin}`;
                        }

                        // 2. Lien produit spécifique
                        const productLink = element.querySelector('a[data-testid=""product-card-link""]');
                        if (productLink && productLink.href) {
                            const asinMatch = productLink.href.match(/\/dp\/([A-Z0-9]{10})/);
                            if (asinMatch) {
                                product.ProductUrl = `https://www.amazon.fr/dp/${asinMatch[1]}`;
                            }
                        }

                        // 3. Titre du produit
                        const titleSelectors = [
                            'a[data-testid=""product-card-link""]',
                            'a[href*=""/dp/""] span',
                            'h3 a',
                            'h2 a',
                            'h3',
                            'h2',
                            '.a-size-base-plus',
                            'img[alt]'
                        ];

                        for (const selector of titleSelectors) {
                            const titleEl = element.querySelector(selector);
                            if (titleEl) {
                                let title = '';
                                if (titleEl.tagName === 'A') {
                                    title = titleEl.textContent.trim() || titleEl.getAttribute('title') || '';
                                } else if (titleEl.tagName === 'IMG') {
                                    title = titleEl.getAttribute('alt') || '';
                                } else {
                                    title = titleEl.textContent.trim();
                                }

                                if (title && title.length > 10 && title.length < 200 && 
                                    !title.includes('€') && !title.includes('%')) {
                                    product.Title = title;
                                    break;
                                }
                            }
                        }

                        // 4. Réduction (sélecteur spécifique identifié)
                        const discountElement = element.querySelector('div[class*=""style_badgeContainer""], div[class^=""style_badgeContainer""]');
                        if (discountElement) {
                            const discountText = discountElement.textContent.trim();
                            if (discountText.includes('%') || discountText.includes('€')) {
                                product.Discount = discountText;
                            }
                        }

                        // 5. Prix actuel
                        const priceSelectors = ['.a-price .a-offscreen', '.a-price-whole', 'span[class*=""price""]'];
                        for (const selector of priceSelectors) {
                            const priceEl = element.querySelector(selector);
                            if (priceEl && priceEl.textContent.includes('€')) {
                                product.Price = priceEl.textContent.trim();
                                break;
                            }
                        }

                        // 6. Image
                        const imgEl = element.querySelector('img');
                        if (imgEl && imgEl.src && !imgEl.src.includes('data:image')) {
                            product.ImageUrl = imgEl.src;
                        }

                        // Validation et ajout
                        if (product.Title && product.ProductUrl) {
                            products.push(product);
                            if (index < 3) {
                                console.log(`✅ Produit ${index + 1}: ${product.Title.substring(0, 40)}...`);
                            }
                        }
                    } catch (error) {
                        console.log(`❌ Erreur produit ${index + 1}:`, error);
                    }
                });

                console.log(`🎯 ${products.length} produits valides extraits`);
                return products;
            }");

            return products?.ToList() ?? new List<ProductInfo>();
        }

        /// <summary>
        /// Fallback vers l'extracteur IA si le scraping direct échoue
        /// </summary>
        private async Task<List<ProductInfo>> ExtractWithAiFallback(IPage page)
        {
            if (_aiExtractor == null)
            {
                return new List<ProductInfo>();
            }

            try
            {
                // Utiliser l'extracteur IA comme fallback
                Console.WriteLine("   🤖 Utilisation du fallback IA...");
                // Note: Cette méthode devrait être adaptée selon l'interface de AiBasedExtractor
                // return await _aiExtractor.ExtractProductsFromPage(page);
                return new List<ProductInfo>(); // Placeholder
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Erreur fallback IA: {ex.Message}");
                return new List<ProductInfo>();
            }
        }

        /// <summary>
        /// Charge plus de contenu en cliquant sur "Afficher plus d'offres"
        /// </summary>
        private async Task LoadMoreDeals(IPage page)
        {
            Console.WriteLine("   🔄 Tentative de chargement de plus d'offres...");
            
            int clickCount = 0;
            int maxClicks = MAX_SCROLL_ATTEMPTS;

            while (clickCount < maxClicks)
            {
                try
                {
                    var buttonClicked = await page.EvaluateFunctionAsync<bool>(@"() => {
                        const selectors = [
                            'button[aria-label*=""Afficher plus""]',
                            'button:contains(""Afficher plus d\'offres"")',
                            'a[aria-label*=""Afficher plus""]',
                            '.a-button-text:contains(""Afficher plus"")'
                        ];

                        for (const selector of selectors) {
                            const button = document.querySelector(selector);
                            if (button && button.offsetParent !== null) {
                                button.click();
                                return true;
                            }
                        }
                        return false;
                    }");

                    if (!buttonClicked)
                    {
                        Console.WriteLine($"   ℹ️ Bouton 'Afficher plus' non trouvé après {clickCount} clics");
                        break;
                    }

                    clickCount++;
                    Console.WriteLine($"   🔄 Clic {clickCount}/{maxClicks} sur 'Afficher plus d'offres'");

                    // Attendre le chargement avec délai aléatoire
                    await Task.Delay(_random.Next(SCROLL_DELAY_MIN, SCROLL_DELAY_MAX));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ Erreur lors du clic {clickCount}: {ex.Message}");
                    break;
                }
            }

            Console.WriteLine($"   ✅ Chargement terminé après {clickCount} clics");
        }

        /// <summary>
        /// Navigue vers la page suivante
        /// </summary>
        private async Task<bool> NavigateToNextPage(IPage page)
        {
            try
            {
                var nextButtonFound = await page.EvaluateFunctionAsync<bool>(@"() => {
                    const nextSelectors = [
                        'a[aria-label=""Aller à la page suivante""]',
                        'a[aria-label=""Go to next page""]',
                        '.a-pagination .a-last a',
                        'a[aria-label*=""suivant""]'
                    ];

                    for (const selector of nextSelectors) {
                        const nextButton = document.querySelector(selector);
                        if (nextButton && !nextButton.classList.contains('a-disabled')) {
                            nextButton.click();
                            return true;
                        }
                    }
                    return false;
                }");

                if (nextButtonFound)
                {
                    await page.WaitForNavigationAsync(new NavigationOptions 
                    { 
                        WaitUntil = new[] { WaitUntilNavigation.Networkidle0 },
                        Timeout = 30000
                    });
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ Erreur navigation: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Génère les liens d'affiliation pour tous les produits
        /// </summary>
        public void GenerateAffiliateLinks(string amazonAssociateTag)
        {
            foreach (var product in _extractedProducts)
            {
                if (!string.IsNullOrEmpty(product.ProductUrl))
                {
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                    }
                }
            }
        }

        /// <summary>
        /// Sauvegarde les produits en JSON
        /// </summary>
        public async Task SaveProductsToJson(string filePath)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_extractedProducts, options);
            await File.WriteAllTextAsync(filePath, json);
            Console.WriteLine($"💾 Produits sauvegardés: {filePath}");
        }

        public List<ProductInfo> GetExtractedProducts() => _extractedProducts;
    }
}
