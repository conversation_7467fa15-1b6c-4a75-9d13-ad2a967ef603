using GenerativeAI;
using GenerativeAI.Types;
using PuppeteerSharp;
using System.Text.Json;
using System.Text;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Générateur de posts Facebook optimisé avec support multimodal
    /// </summary>
    internal class FacebookPostGenerator
    {
        private readonly GenerativeModel _model;

        public FacebookPostGenerator(string apiKey)
        {
            _model = new GenerativeModel(apiKey, "gemini-2.5-flash-lite-preview-06-17");
        }

        /// <summary>
        /// Génère un post Facebook depuis le contenu d'une page
        /// </summary>
        public async Task<string> GeneratePostFromPageContent(IPage page, string affiliateLink)
        {
            try
            {
                // Essayer d'abord avec l'URL directe (plus efficace)
                var urlResult = await GeneratePostFromUrl(page.Url, affiliateLink);

                // Si erreur, utiliser l'extraction de contenu
                return urlResult.Contains("Erreur")
                    ? await GeneratePostFromPageContentFallback(page, affiliateLink)
                    : urlResult;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook : {ex.Message}");
                return await GeneratePostFromPageContentFallback(page, affiliateLink);
            }
        }

        /// <summary>
        /// Génère un post Facebook depuis le contenu d'une page AVEC screenshot (pour pages produits individuels)
        /// </summary>
        public async Task<string> GeneratePostFromPageContentWithScreenshot(IPage page, string affiliateLink, string screenshotPath)
        {
            try
            {
                Console.WriteLine($"📸 Génération post Facebook avec screenshot pour page produit individuel");

                // Si on a un screenshot, utiliser l'analyse multimodale
                if (!string.IsNullOrEmpty(screenshotPath) && File.Exists(screenshotPath))
                {
                    Console.WriteLine($"📸 Utilisation du screenshot : {Path.GetFileName(screenshotPath)}");

                    // Extraire le contenu de la page
                    var pageContent = await ExtractPageContent(page);
                    var prompt = CreateContentBasedPromptWithScreenshot(page.Url, pageContent, affiliateLink);

                    // Appeler Gemini avec le screenshot
                    var result = await CallGeminiAPIWithImage(prompt, screenshotPath);

                    if (IsValidResponse(result))
                    {
                        Console.WriteLine($"✅ Post généré avec succès via analyse multimodale");
                        return result;
                    }
                }

                // Fallback vers la méthode normale sans screenshot
                Console.WriteLine($"🔄 Fallback vers génération sans screenshot");
                return await GeneratePostFromPageContent(page, affiliateLink);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur génération avec screenshot : {ex.Message}");
                // Fallback vers la méthode normale
                return await GeneratePostFromPageContent(page, affiliateLink);
            }
        }

        /// <summary>
        /// Génère un post depuis une URL avec UrlContext optimisé
        /// </summary>
        public async Task<string> GeneratePostFromUrl(string productUrl, string affiliateLink)
        {
            try
            {
                if (!IsValidUrl(productUrl))
                {
                    Console.WriteLine($"URL invalide : {productUrl}");
                    return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
                }

                // Essayer avec AddRemoteFile (UrlContext)
                try
                {
                    var request = CreateOptimizedRequest();
                    request.AddRemoteFile(productUrl, "text/html");
                    request.AddText(CreateUrlContextPrompt(productUrl, affiliateLink));

                    var response = await _model.GenerateContentAsync(request);
                    var result = response.Text();

                    return IsValidResponse(result ?? "") ? (result ?? "") : await GeneratePostFromUrlFallback(productUrl, affiliateLink);
                }
                catch (Exception urlEx)
                {
                    Console.WriteLine($"Erreur avec AddRemoteFile : {urlEx.Message}");
                    return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook : {ex.Message}");
                return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
            }
        }

        /// <summary>
        /// Appelle l'API Gemini avec un prompt simple
        /// </summary>
        public async Task<string> CallGeminiAPI(string prompt)
        {
            try
            {
                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                return HandleApiError(ex);
            }
        }

        /// <summary>
        /// Appelle l'API Gemini avec support d'image (analyse multimodale optimisée)
        /// </summary>
        public async Task<string> CallGeminiAPIWithImage(string prompt, string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath))
                {
                    Console.WriteLine($"⚠️ Fichier image non trouvé : {imagePath}");
                    return await CallGeminiAPI(prompt);
                }

                // Créer un prompt enrichi avec contexte visuel
                var enrichedPrompt = CreateEnrichedPromptWithVisualContext(prompt, imagePath);
                
                Console.WriteLine($"📸 Screenshot disponible pour référence : {Path.GetFileName(imagePath)}");
                Console.WriteLine($"📝 Utilisation du prompt enrichi avec contexte visuel...");
                
                var response = await _model.GenerateContentAsync(enrichedPrompt);
                return response.Text() ?? "Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de l'appel à l'API Gemini avec image : {ex.Message}");
                Console.WriteLine($"🔄 Tentative de fallback vers texte seul...");
                return await CallGeminiAPI(prompt);
            }
        }

        /// <summary>
        /// Méthode de fallback pour l'extraction de contenu
        /// </summary>
        private async Task<string> GeneratePostFromPageContentFallback(IPage page, string affiliateLink)
        {
            try
            {
                var pageContent = await ExtractPageContent(page);
                var prompt = CreateContentBasedPrompt(page.Url, pageContent, affiliateLink);
                
                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "Erreur : Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook (fallback) : {ex.Message}");
                return $"Erreur lors de la génération du contenu : {ex.Message}";
            }
        }

        /// <summary>
        /// Méthode de fallback pour URL
        /// </summary>
        private async Task<string> GeneratePostFromUrlFallback(string productUrl, string affiliateLink)
        {
            try
            {
                var asin = ExtractAsinFromUrl(productUrl);
                var productInfo = asin != null ? $"ASIN: {asin}" : "URL fournie";
                var prompt = CreateFallbackPrompt(productUrl, affiliateLink, productInfo);
                
                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "Erreur : Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook (fallback) : {ex.Message}");
                return $"Erreur lors de la génération du contenu : {ex.Message}";
            }
        }

        // Méthodes utilitaires optimisées
        private GenerateContentRequest CreateOptimizedRequest()
        {
            var request = new GenerateContentRequest();
            request.GenerationConfig = new GenerationConfig()
            {
                Temperature = 0.7f,
                ResponseMimeType = "text/plain"
            };
            return request;
        }

        private string CreateEnrichedPromptWithVisualContext(string prompt, string imagePath)
        {
            return $@"{prompt}

📸 ANALYSE VISUELLE COMPLÉMENTAIRE :
Une capture d'écran de la page Amazon est disponible pour validation visuelle.
Utilise cette image pour :
1. Vérifier que les produits détectés dans le HTML sont bien visibles
2. Identifier des éléments visuels (badges promo, prix barrés, etc.)
3. Détecter des produits supplémentaires qui pourraient être mal structurés dans le HTML
4. Valider la cohérence entre le code HTML et l'affichage réel

L'image capture l'état actuel de la page Amazon avec tous les produits visibles.
Croise ces informations visuelles avec l'analyse HTML pour une extraction optimale.";
        }

        private string CreateUrlContextPrompt(string productUrl, string affiliateLink)
        {
            var systemPrompt = GetSystemPrompt();
            return $@"{systemPrompt}

Analyse la page Amazon fournie et rédige un poste Facebook optimisé pour la conversion d'un produit en affiliation.

URL du produit : {productUrl}
Utilise ce lien d'affiliation : {affiliateLink}

Suis exactement les instructions du système pour créer un post Facebook parfait en te basant sur le contenu de la page Amazon fournie.";
        }

        private string CreateContentBasedPrompt(string url, string pageContent, string affiliateLink)
        {
            var systemPrompt = GetSystemPrompt();
            return $@"{systemPrompt}

Rédige un poste Facebook optimisé pour la conversion d'un produit en affiliation avec Amazon suivant :

URL de la page : {url}

Contenu de la page :
{pageContent}

Utilise ce lien d'affiliation : {affiliateLink}

Suis exactement les instructions du système pour créer un post Facebook parfait.";
        }

        /// <summary>
        /// Crée un prompt enrichi avec screenshot pour les pages produits individuels
        /// </summary>
        private string CreateContentBasedPromptWithScreenshot(string url, string pageContent, string affiliateLink)
        {
            var systemPrompt = GetSystemPrompt();
            return $@"{systemPrompt}

📸 ANALYSE MULTIMODALE - PAGE PRODUIT AMAZON INDIVIDUEL

Tu as accès à :
1. Le contenu HTML extrait de la page produit
2. Un screenshot de la page produit pour validation visuelle

URL de la page produit : {url}

Contenu extrait de la page :
{pageContent}

Utilise ce lien d'affiliation : {affiliateLink}

INSTRUCTIONS SPÉCIALES POUR L'ANALYSE MULTIMODALE :
1. Utilise le screenshot pour vérifier et compléter les informations extraites du HTML
2. Identifie visuellement les éléments importants : prix, réductions, badges, avis
3. Détecte les éléments visuels qui pourraient manquer dans le HTML (images produit, badges promo, etc.)
4. Valide la cohérence entre le contenu HTML et l'affichage visuel
5. Utilise les informations visuelles pour enrichir le post Facebook

Le screenshot montre la page produit Amazon telle qu'elle apparaît à l'utilisateur.
Croise ces informations visuelles avec le contenu HTML pour créer un post Facebook parfait et précis.

Suis exactement les instructions du système pour créer un post Facebook optimisé.";
        }

        private string CreateFallbackPrompt(string productUrl, string affiliateLink, string productInfo)
        {
            var systemPrompt = GetSystemPrompt();
            return $@"{systemPrompt}

UTILISE UrlContext pour analyser directement la page Amazon et créer un post Facebook optimisé.

URL du produit Amazon : {productUrl}
Lien d'affiliation à utiliser : {affiliateLink}

INSTRUCTIONS POUR UrlContext:
1. Analyse le contenu complet de la page Amazon fournie
2. Extrait automatiquement :
   - Le titre exact du produit
   - Le prix actuel et les réductions
   - Les avis et notes clients
   - Les caractéristiques principales
   - Les images du produit

3. Crée un post Facebook optimisé avec :
   - Titre accrocheur avec emojis
   - Prix et réductions mis en avant
   - Points forts du produit
   - Call-to-action efficace
   - Hashtags pertinents
   - Mention légale d'affiliation

4. Format de post attendu :
💥 [TITRE PRODUIT] - [RÉDUCTION]% ! 💥
💰 Prix : [PRIX ACTUEL] au lieu de [PRIX ORIGINAL]
⭐ Note : [NOTE]/5 ([NOMBRE] avis)

🎯 Points forts :
✅ [Caractéristique 1]
✅ [Caractéristique 2]
✅ [Caractéristique 3]

👉 Profitez de cette offre : {affiliateLink}

#Amazon #BonPlan #Promo #Deal #Shopping

En tant que partenaire Amazon, je perçois une rémunération grâce aux achats éligibles.

ANALYSE LA PAGE ET CRÉE LE POST MAINTENANT :";
        }

        private string HandleApiError(Exception ex)
        {
            if (ex.Message.Contains("RESOURCE_EXHAUSTED") || ex.Message.Contains("quota") || ex.Message.Contains("429"))
            {
                Console.WriteLine("⚠️ QUOTA API GEMINI ÉPUISÉ - Passage en mode de secours");
                Console.WriteLine("💡 Solutions :");
                Console.WriteLine("   1. Vérifiez votre quota sur https://aistudio.google.com/");
                Console.WriteLine("   2. Attendez la réinitialisation du quota (souvent quotidien)");
                Console.WriteLine("   3. Utilisez une autre clé API si disponible");
                Console.WriteLine("   4. Réduisez la taille des requêtes (moins de pages/contenu)");
                return "QUOTA_EXHAUSTED";
            }

            Console.WriteLine($"❌ Erreur lors de l'appel à l'API Gemini : {ex.Message}");
            return $"Erreur : {ex.Message}";
        }

        private static bool IsValidUrl(string url) => 
            !string.IsNullOrEmpty(url) && Uri.IsWellFormedUriString(url, UriKind.Absolute);

        private static bool IsValidResponse(string result) => 
            !string.IsNullOrEmpty(result) && !result.Contains("Erreur") && !result.Contains("je suis prêt");

        private static string? ExtractAsinFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return null;
            var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
            return asinMatch.Success ? asinMatch.Groups[1].Value : null;
        }

        private static string GetSystemPrompt()
        {
            return @"Tu es un expert en marketing d'affiliation Amazon et en création de contenu Facebook optimisé pour la conversion.

Ton rôle est de créer des posts Facebook accrocheurs et persuasifs pour des produits Amazon en affiliation.

Instructions spécifiques :
1. Commence TOUJOURS par annoncer la valeur de la réduction du prix et la promo, le prix final et le prix initial barré, en utilisant le format suivant :
   💥 Économisez [pourcentage]% DÈS MAINTENANT sur [nom du produit]! 💥
   PRIX EXCEPTIONNEL : [prix final] € au lieu de [prix initial] € !
   👉 Ne manquez pas cette offre, cliquez ici : [lien d'affiliation]

2. Si la réduction est variable, ajoute le mot ""jusqu'à"" avant la réduction.
3. Fais un post court et accrocheur mettant l'accent sur la marque et les caractéristiques du produit.
4. Affiche le nombre d'étoiles (sous forme d'étoiles ⭐) et le nombre d'avis.
5. Consulte les avis utilisateurs et cite le meilleur avis le plus récent.
6. Indique si c'est le cas que la livraison et le retour sont gratuits et qu'il est éligible à la livraison Prime.
7. N'ajoute aucune référence dans ta réponse.
8. Fais un appel à l'action rapide dès le début du poste avec le lien d'affiliation.
9. Ajoute les hashtags pertinents pour augmenter la visibilité du post.
10. Utilise des pictogrammes à la place des listes à puces.
11. Adapte le ton suivant le niveau de la réduction.
12. Ajoute en bas du poste : ""En tant que partenaire Amazon, les liens #Amazon sont rémunérés""
13. Utilise le lien d'affiliation fourni dans la demande.";
        }

        /// <summary>
        /// Extrait le contenu de la page de manière optimisée
        /// </summary>
        private async Task<string> ExtractPageContent(IPage page)
        {
            try
            {
                var content = await page.EvaluateFunctionAsync<string>(@"() => {
                    const result = {
                        title: '',
                        price: '',
                        originalPrice: '',
                        discount: '',
                        rating: '',
                        reviewCount: '',
                        features: [],
                        description: '',
                        availability: '',
                        prime: false,
                        freeShipping: false,
                        freeReturns: false,
                        reviews: []
                    };

                    // Extraction optimisée avec sélecteurs multiples
                    const selectors = {
                        title: ['#productTitle', '.product-title', 'h1'],
                        price: ['.a-price-whole', '.a-offscreen', '.a-price .a-offscreen'],
                        originalPrice: ['.a-text-price .a-offscreen', '.a-price.a-text-price .a-offscreen'],
                        discount: ['.savingsPercentage', '.a-color-price'],
                        rating: ['.a-icon-alt', '.a-star-medium .a-icon-alt'],
                        reviewCount: ['#acrCustomerReviewText', '.a-link-normal'],
                        availability: ['#availability span', '.a-color-success', '.a-color-state']
                    };

                    // Fonction utilitaire pour extraire le texte du premier élément trouvé
                    const extractText = (selectorArray) => {
                        for (const selector of selectorArray) {
                            const element = document.querySelector(selector);
                            if (element && element.textContent.trim()) {
                                return element.textContent.trim();
                            }
                        }
                        return '';
                    };

                    // Extraction des données principales
                    result.title = extractText(selectors.title);
                    result.price = extractText(selectors.price);
                    result.originalPrice = extractText(selectors.originalPrice);
                    result.discount = extractText(selectors.discount);
                    result.rating = extractText(selectors.rating);
                    result.reviewCount = extractText(selectors.reviewCount);
                    result.availability = extractText(selectors.availability);

                    // Caractéristiques
                    const featureElements = document.querySelectorAll('#feature-bullets ul li, .a-unordered-list .a-list-item');
                    featureElements.forEach(el => {
                        const text = el.textContent.trim();
                        if (text && text.length > 10) result.features.push(text);
                    });

                    // Description
                    const descElement = document.querySelector('#productDescription, .product-description');
                    if (descElement) result.description = descElement.textContent.trim();

                    // Prime et livraison
                    result.prime = !!document.querySelector('.a-icon-prime, [data-csa-c-type=""element""][data-csa-c-element-id=""prime-logo""]');

                    const freeShippingElement = document.querySelector('[data-feature-name=""deliveryMessage""], .a-color-secondary');
                    if (freeShippingElement && freeShippingElement.textContent.toLowerCase().includes('gratuit')) {
                        result.freeShipping = true;
                    }

                    const freeReturnsElement = document.querySelector('[data-feature-name=""returns""]');
                    if (freeReturnsElement && freeReturnsElement.textContent.toLowerCase().includes('gratuit')) {
                        result.freeReturns = true;
                    }

                    // Avis récents (optimisé)
                    const reviewElements = document.querySelectorAll('[data-hook=""review-body""] span, .cr-original-review-text');
                    reviewElements.forEach((el, index) => {
                        if (index < 3) {
                            const text = el.textContent.trim();
                            if (text && text.length > 20) result.reviews.push(text);
                        }
                    });

                    return JSON.stringify(result, null, 2);
                }");

                return content;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'extraction du contenu : {ex.Message}");
                return $"Erreur lors de l'extraction : {ex.Message}";
            }
        }
    }
}
